import 'package:chess_app/bloc/cubits/game_cubit.dart';
import 'package:chess_app/bloc/states/game_state.dart';
import 'package:chess_app/config/colors.dart';
import 'package:chess_app/screens/settings_screen.dart';
import 'package:chess_app/ui/board_widget.dart';
import 'package:chess_app/ui/lost_figures_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({Key? key}) : super(key: key);

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  @override
  void initState() {
    super.initState();
    // Start the battle when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GetIt.I<GameCubit>().startBattle();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: const Text('Chess Game'),
        backgroundColor: Colors.brown[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: BlocListener<GameCubit, GameState>(
        bloc: GetIt.I<GameCubit>(),
        listener: (context, state) {
          if (state.isGameOver) {
            _showGameOverDialog(context, state);
          }
        },
        child: BlocBuilder<GameCubit, GameState>(
          bloc: GetIt.I<GameCubit>(),
          builder: (context, state) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Game status indicator
                Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _getGameStatusText(state),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: state.isGameOver ? Colors.red : Colors.black,
                    ),
                  ),
                ),
                LostFiguresWidget(figures: state.board.blackLost.figures),
                BoardWidget(
                  availablePositionsHash: state.availablePositionsHash,
                  board: state.board,
                  selectedCell: state.selectedCell,
                ),
                LostFiguresWidget(figures: state.board.whiteLost.figures),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showGameOverDialog(BuildContext context, GameState state) {
    String title;
    String message;

    if (state.winner != null) {
      final winnerName = state.winner!.name;
      title = 'Game Over!';
      message =
          '${winnerName.toUpperCase()} wins!\n\nThe ${winnerName == 'white' ? 'black' : 'white'} king has been captured.';
    } else {
      title = 'Game Over!';
      message = 'The game ended in a draw.';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
              child: const Text('New Game'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('View Board'),
            ),
          ],
        );
      },
    );
  }

  String _getGameStatusText(GameState state) {
    if (state.isGameOver) {
      if (state.winner != null) {
        final winnerName = state.winner!.name;
        return '🎉 GAME OVER - ${winnerName.toUpperCase()} WINS! 🎉';
      } else {
        return '🤝 GAME OVER - DRAW! 🤝';
      }
    }

    if (state.isAIthinking) {
      return 'AI is thinking...';
    }

    final currentPlayer = state.activeColor.name;
    return '${currentPlayer.toUpperCase()} to move';
  }
}
