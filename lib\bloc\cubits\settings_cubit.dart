import 'package:chess_app/bloc/states/settings_state.dart';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/lost_figures.dart';
import 'package:chess_app/models/player.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SettingsCubit(SettingsState initialState) : super(initialState);

  factory SettingsCubit.initial() {
    final board =
        Board(cells: [], whiteLost: LostFigures(), blackLost: LostFigures());
    board.createCells();
    board.putFigures();

    return SettingsCubit(SettingsState(
      whitePlayer: Player.human(),
      blackPlayer: Player.ai(),
      difficulty: 5, // Tăng độ khó lên tối đa
    ));
  }

  void setWhitePlayer(Player player) {
    emit(state.copyWith(whitePlayer: player));
  }

  void setBlackPlayer(Player player) {
    emit(state.copyWith(blackPlayer: player));
  }

  void setDifficulty(int difficulty) {
    emit(state.copyWith(difficulty: difficulty));
  }

  void setGameMode(GameMode mode) {
    switch (mode) {
      case GameMode.humanVsHuman:
        emit(state.copyWith(
          whitePlayer: Player.human(),
          blackPlayer: Player.human(),
        ));
        break;
      case GameMode.humanVsAI:
        emit(state.copyWith(
          whitePlayer: Player.human(),
          blackPlayer: Player.ai(),
        ));
        break;
      case GameMode.aiVsHuman:
        emit(state.copyWith(
          whitePlayer: Player.ai(),
          blackPlayer: Player.human(),
        ));
        break;
      case GameMode.aiVsAI:
        emit(state.copyWith(
          whitePlayer: Player.ai(),
          blackPlayer: Player.ai(),
        ));
        break;
    }
  }
}

enum GameMode {
  humanVsHuman,
  humanVsAI,
  aiVsHuman,
  aiVsAI,
}
