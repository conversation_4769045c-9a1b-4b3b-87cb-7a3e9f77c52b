import 'package:stockfish/stockfish.dart';
import 'package:chess_app/services/ai/simple_ai.dart';
import 'package:chess_app/services/ai/smart_ai.dart';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/game_colors.dart';

/// Facade for all operations with chess engine
/// only here we interact with it
abstract class AIManager {
  void initEngine();
  void disposeEngine();

  String getCurrentState();
  void setCommand(String uciCommand);

  void registerOutputCallback();
}

enum AIEngines {
  stockFish,
  simple,
  advanced,
}

/// stock-fish manager
class StockFishAIManager implements AIManager {
  late Stockfish _stockfish;
  String _currentState = '';
  Function(String)? _outputCallback;

  @override
  void initEngine() {
    _stockfish = Stockfish();
    _stockfish.stdout.listen((line) {
      _currentState = line;
      if (_outputCallback != null) {
        _outputCallback!(line);
      }
    });

    // Initialize UCI protocol
    setCommand('uci');
    setCommand('isready');
    setCommand('ucinewgame');
  }

  @override
  void disposeEngine() {
    _stockfish.stdin = 'quit';
  }

  @override
  String getCurrentState() {
    return _currentState;
  }

  @override
  void setCommand(String uciCommand) {
    _stockfish.stdin = uciCommand;
  }

  @override
  void registerOutputCallback() {
    // Output callback is already registered in initEngine
  }

  Future<String> getBestMove(String fen, {int depth = 10}) async {
    try {
      setCommand('position fen $fen');
      setCommand('go depth $depth');

      // Wait for bestmove response
      await Future.delayed(const Duration(milliseconds: 100));

      // Parse the bestmove from the output
      final lines = _currentState.split('\n');
      for (final line in lines.reversed) {
        if (line.startsWith('bestmove')) {
          final parts = line.split(' ');
          if (parts.length >= 2) {
            return parts[1];
          }
        }
      }
    } catch (e) {
      // Stockfish failed, return empty string
    }

    return '';
  }
}

/// Simple AI manager using basic chess logic
class SimpleAIManager implements AIManager {
  final SimpleAI _simpleAI = SimpleAI();

  @override
  void initEngine() {
    // No initialization needed for simple AI
  }

  @override
  void disposeEngine() {
    // No cleanup needed for simple AI
  }

  @override
  String getCurrentState() {
    return 'Simple AI Ready';
  }

  @override
  void setCommand(String uciCommand) {
    // Simple AI doesn't use UCI commands
  }

  @override
  void registerOutputCallback() {
    // No callback needed for simple AI
  }

  Future<String> getBestMove(Board board, GameColors color,
      {int depth = 10}) async {
    final move = await _simpleAI.getBestMove(board, color);
    return move ?? '';
  }
}

/// Advanced AI manager with strategic thinking
class AdvancedAIManager implements AIManager {
  late AdvancedAI _advancedAI;
  final int difficulty;

  AdvancedAIManager({required this.difficulty}) {
    _advancedAI = AdvancedAI(difficulty: difficulty);
  }

  @override
  void initEngine() {
    // No initialization needed for advanced AI
  }

  @override
  void disposeEngine() {
    // No cleanup needed for advanced AI
  }

  @override
  String getCurrentState() {
    return 'Advanced AI Ready (Difficulty: $difficulty)';
  }

  @override
  void setCommand(String uciCommand) {
    // Advanced AI doesn't use UCI commands
  }

  @override
  void registerOutputCallback() {
    // No callback needed for advanced AI
  }

  Future<String> getBestMove(Board board, GameColors color,
      {int depth = 10}) async {
    final move = await _advancedAI.getBestMove(board, color);
    return move ?? '';
  }
}

AIManager createAIManager(
    {AIEngines engine = AIEngines.advanced, int difficulty = 5}) {
  switch (engine) {
    case AIEngines.stockFish:
      return StockFishAIManager();
    case AIEngines.simple:
      return SimpleAIManager();
    case AIEngines.advanced:
      return AdvancedAIManager(difficulty: difficulty);
  }
}
