import 'package:chess_app/bloc/cubits/settings_cubit.dart';
import 'package:chess_app/bloc/states/game_state.dart';
import 'package:chess_app/bloc/states/settings_state.dart';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/game_colors.dart';
import 'package:chess_app/models/lost_figures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:get_it/get_it.dart';

class GameCubit extends Cubit<GameState> {
  GameCubit(GameState initialState) : super(initialState);

  factory GameCubit.initial() {
    final board =
        Board(cells: [], whiteLost: LostFigures(), blackLost: LostFigures());
    board.createCells();
    board.putFigures();

    return GameCubit(GameState(
        activeColor: GameColors.white,
        selectedCell: null,
        board: board,
        isAIthinking: false,
        availablePositionsHash: {}));
  }

  void startBattle() {
    final settings = _getSettings();

    if (settings.hasAI && !settings.whitePlayer.isHuman) {
      _scheduleAIMove();
    }
  }

  void selectCell(Cell? newCell) {
    emit(state.copyWith(
        selectedCell: newCell,
        availablePositionsHash:
            state.board.getAvailablePositionsHash(newCell)));
  }

  void moveFigure(Cell toCell) async {
    if (state.selectedCell == null) {
      return;
    }

    state.selectedCell!.moveFigure(toCell);

    // Update the state after the move
    emit(state.copyWith(
        board: state.board.copyThis(),
        activeColor: state.activeColor.getOpposite()));

    selectCell(null);

    // if we play with AI
    if (_getSettings().hasAI) {
      final nextColor = state.activeColor.getOpposite();
      final nextPlayer = _getSettings().getPlayerByColor(nextColor);

      if (!nextPlayer.isHuman) {
        await _scheduleAIMove();
      }
    }
  }

  Future<void> _scheduleAIMove() async {
    emit(state.copyWith(isAIthinking: true));
    // TODO: Implement AI logic here
    emit(state.copyWith(isAIthinking: false));
  }

  SettingsState _getSettings() {
    return GetIt.I<SettingsCubit>().state;
  }
}
