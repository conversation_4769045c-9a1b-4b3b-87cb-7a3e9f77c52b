import 'package:chess_app/bloc/cubits/settings_cubit.dart';
import 'package:chess_app/bloc/states/game_state.dart';
import 'package:chess_app/bloc/states/settings_state.dart';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/game_colors.dart';
import 'package:chess_app/models/lost_figures.dart';
import 'package:chess_app/services/ai/ai_manager.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:get_it/get_it.dart';

class GameCubit extends Cubit<GameState> {
  late AIManager _aiManager;

  GameCubit(GameState initialState, {AIEngines aiEngine = AIEngines.simple})
      : super(initialState) {
    _aiManager = createAIManager(engine: aiEngine);
    _aiManager.initEngine();
  }

  factory GameCubit.initial({AIEngines aiEngine = AIEngines.simple}) {
    final board =
        Board(cells: [], whiteLost: LostFigures(), blackLost: LostFigures());
    board.createCells();
    board.putFigures();

    return GameCubit(
        GameState(
            activeColor: GameColors.white,
            selectedCell: null,
            board: board,
            isAIthinking: false,
            availablePositionsHash: {}),
        aiEngine: aiEngine);
  }

  void startBattle() {
    final settings = _getSettings();

    if (settings.hasAI && !settings.whitePlayer.isHuman) {
      _scheduleAIMove();
    }
  }

  void selectCell(Cell? newCell) {
    emit(state.copyWith(
        selectedCell: newCell,
        availablePositionsHash:
            state.board.getAvailablePositionsHash(newCell)));
  }

  void moveFigure(Cell toCell) async {
    if (state.selectedCell == null) {
      return;
    }

    state.selectedCell!.moveFigure(toCell);

    // Update the state after the move
    emit(state.copyWith(
        board: state.board.copyThis(),
        activeColor: state.activeColor.getOpposite()));

    selectCell(null);

    // if we play with AI
    if (_getSettings().hasAI) {
      final nextColor = state.activeColor.getOpposite();
      final nextPlayer = _getSettings().getPlayerByColor(nextColor);

      if (!nextPlayer.isHuman) {
        await _scheduleAIMove();
      }
    }
  }

  Future<void> _scheduleAIMove() async {
    emit(state.copyWith(isAIthinking: true));

    try {
      String bestMove = '';

      // Try to use the AI manager
      if (_aiManager is SimpleAIManager) {
        final simpleManager = _aiManager as SimpleAIManager;
        bestMove = await simpleManager.getBestMove(
            state.board, state.activeColor,
            depth: _getSettings().difficulty * 3);
      } else if (_aiManager is StockFishAIManager) {
        // Get current board state in FEN notation
        final fen = state.board.toFEN(activeColor: state.activeColor);
        final stockfishManager = _aiManager as StockFishAIManager;
        bestMove = await stockfishManager.getBestMove(fen,
            depth: _getSettings().difficulty * 3);
      }

      if (bestMove.isNotEmpty) {
        // Make the AI move
        final success = state.board.makeUCIMove(bestMove);
        if (success) {
          emit(state.copyWith(
            board: state.board.copyThis(),
            activeColor: state.activeColor.getOpposite(),
            isAIthinking: false,
          ));
          selectCell(null);
        }
      }
    } catch (e) {
      // AI move failed, just continue
    }

    emit(state.copyWith(isAIthinking: false));
  }

  SettingsState _getSettings() {
    return GetIt.I<SettingsCubit>().state;
  }

  @override
  Future<void> close() {
    _aiManager.disposeEngine();
    return super.close();
  }
}
