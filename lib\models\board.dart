import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/cell_calculator.dart';
import 'package:chess_app/models/cell_position.dart';
import 'package:chess_app/models/figure.dart';
import 'package:chess_app/models/figure_types.dart';
import 'package:chess_app/models/figures/bishop.dart';
import 'package:chess_app/models/figures/king.dart';
import 'package:chess_app/models/figures/knight.dart';
import 'package:chess_app/models/figures/pawn.dart';
import 'package:chess_app/models/figures/queen.dart';
import 'package:chess_app/models/figures/rook.dart';
import 'package:chess_app/models/game_colors.dart';
import 'package:chess_app/models/lost_figures.dart';

const boardSize = 8;

class Board {
  final List<List<Cell>> cells;

  final LostFigures blackLost;
  final LostFigures whiteLost;

  Board(
      {required this.cells, required this.blackLost, required this.whiteLost});

  void createCells() {
    for (int y = 0; y < boardSize; y++) {
      final List<Cell> row = [];

      for (int x = 0; x < boardSize; x++) {
        if ((x + y) % 2 != 0) {
          row.add(Cell.white(board: this, position: CellPosition(x, y)));
        } else {
          row.add(Cell.black(board: this, position: CellPosition(x, y)));
        }
      }

      cells.add(row);
    }
  }

  void pushFigureLoLost(Figure lostFigure) {
    if (lostFigure.isBlack) {
      blackLost.push(lostFigure);
    }

    if (lostFigure.isWhite) {
      whiteLost.push(lostFigure);
    }
  }

  Set<String> getAvailablePositionsHash(Cell? selectedCell) {
    return CellCalculator.getAvailablePositionsHash(this, selectedCell);
  }

  Board copyThis() {
    // Create new board first
    final newBoard = Board(
      cells: [],
      blackLost: LostFigures(),
      whiteLost: LostFigures(),
    );

    // Create deep copy of cells with correct board reference
    for (int y = 0; y < boardSize; y++) {
      final List<Cell> row = [];
      for (int x = 0; x < boardSize; x++) {
        final originalCell = cells[y][x];
        final newCell = Cell(
          board: newBoard,
          position: CellPosition(x, y),
          color: originalCell.color,
        );

        // Copy figure if exists
        if (originalCell.occupied) {
          final originalFigure = originalCell.getFigure()!;
          // Create new figure of same type and color
          switch (originalFigure.type) {
            case FigureTypes.pawn:
              Pawn(color: originalFigure.color, cell: newCell);
              break;
            case FigureTypes.rook:
              Rook(color: originalFigure.color, cell: newCell);
              break;
            case FigureTypes.knight:
              Knight(color: originalFigure.color, cell: newCell);
              break;
            case FigureTypes.bishop:
              Bishop(color: originalFigure.color, cell: newCell);
              break;
            case FigureTypes.queen:
              Queen(color: originalFigure.color, cell: newCell);
              break;
            case FigureTypes.king:
              King(color: originalFigure.color, cell: newCell);
              break;
          }
        }

        row.add(newCell);
      }
      newBoard.cells.add(row);
    }

    return newBoard;
  }

  /// Convert current board state to FEN notation for Stockfish
  String toFEN(
      {required GameColors activeColor,
      int halfmoveClock = 0,
      int fullmoveNumber = 1}) {
    String fen = '';

    // Board position
    for (int y = 0; y < boardSize; y++) {
      int emptyCount = 0;
      for (int x = 0; x < boardSize; x++) {
        final cell = getCellAt(x, y);
        if (cell.occupied) {
          if (emptyCount > 0) {
            fen += emptyCount.toString();
            emptyCount = 0;
          }
          fen += _getFENCharForFigure(cell.getFigure()!);
        } else {
          emptyCount++;
        }
      }
      if (emptyCount > 0) {
        fen += emptyCount.toString();
      }
      if (y < boardSize - 1) {
        fen += '/';
      }
    }

    // Active color
    fen += ' ${activeColor == GameColors.white ? 'w' : 'b'}';

    // Castling availability (simplified - assume no castling for now)
    fen += ' -';

    // En passant target square (simplified - assume none for now)
    fen += ' -';

    // Halfmove clock
    fen += ' $halfmoveClock';

    // Fullmove number
    fen += ' $fullmoveNumber';

    return fen;
  }

  String _getFENCharForFigure(Figure figure) {
    String char = '';
    switch (figure.type) {
      case FigureTypes.pawn:
        char = 'p';
        break;
      case FigureTypes.rook:
        char = 'r';
        break;
      case FigureTypes.knight:
        char = 'n';
        break;
      case FigureTypes.bishop:
        char = 'b';
        break;
      case FigureTypes.queen:
        char = 'q';
        break;
      case FigureTypes.king:
        char = 'k';
        break;
    }
    return figure.color == GameColors.white ? char.toUpperCase() : char;
  }

  /// Convert UCI move notation (e.g., "e2e4" or "e7e8q" for promotion) to board coordinates
  bool makeUCIMove(String uciMove) {
    if (uciMove.length < 4) return false;

    final fromSquare = uciMove.substring(0, 2);
    final toSquare = uciMove.substring(2, 4);

    // Check for promotion notation (e.g., "e7e8q")
    final hasPromotion = uciMove.length == 5;
    final promotionPiece = hasPromotion ? uciMove[4].toLowerCase() : null;

    final fromPos = _uciToPosition(fromSquare);
    final toPos = _uciToPosition(toSquare);

    if (fromPos == null || toPos == null) return false;

    final fromCell = getCellAt(fromPos.x, fromPos.y);
    final toCell = getCellAt(toPos.x, toPos.y);

    if (!fromCell.occupied) return false;

    // Make the move (pawn promotion is handled automatically in moveFigure)
    fromCell.moveFigure(toCell);
    return true;
  }

  CellPosition? _uciToPosition(String uciSquare) {
    if (uciSquare.length != 2) return null;

    final file = uciSquare[0].toLowerCase();
    final rank = uciSquare[1];

    final x = file.codeUnitAt(0) - 'a'.codeUnitAt(0);
    final y = 8 - int.parse(rank);

    if (x < 0 || x >= 8 || y < 0 || y >= 8) return null;

    return CellPosition(x, y);
  }

  Cell getCellAt(int x, int y) {
    return cells[y][x];
  }

  putFigures() {
    _putPawns();
    _putBishops();
    _putKnights();
    _putRooks();
    _putQueens();
    _putKings();
  }

  _putPawns() {
    for (int i = 0; i < 8; i++) {
      Pawn(color: GameColors.white, cell: getCellAt(i, 6));
      Pawn(color: GameColors.black, cell: getCellAt(i, 1));
    }
  }

  _putBishops() {
    Bishop(color: GameColors.white, cell: getCellAt(2, 7));
    Bishop(color: GameColors.white, cell: getCellAt(5, 7));
    Bishop(color: GameColors.black, cell: getCellAt(2, 0));
    Bishop(color: GameColors.black, cell: getCellAt(5, 0));
  }

  _putKnights() {
    Knight(color: GameColors.white, cell: getCellAt(1, 7));
    Knight(color: GameColors.white, cell: getCellAt(6, 7));
    Knight(color: GameColors.black, cell: getCellAt(1, 0));
    Knight(color: GameColors.black, cell: getCellAt(6, 0));
  }

  _putRooks() {
    Rook(color: GameColors.white, cell: getCellAt(0, 7));
    Rook(color: GameColors.white, cell: getCellAt(7, 7));
    Rook(color: GameColors.black, cell: getCellAt(0, 0));
    Rook(color: GameColors.black, cell: getCellAt(7, 0));
  }

  _putKings() {
    King(color: GameColors.white, cell: getCellAt(4, 7));
    King(color: GameColors.black, cell: getCellAt(4, 0));
  }

  _putQueens() {
    Queen(color: GameColors.white, cell: getCellAt(3, 7));
    Queen(color: GameColors.black, cell: getCellAt(3, 0));
  }
}
