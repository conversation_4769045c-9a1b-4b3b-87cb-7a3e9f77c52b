import 'dart:math' as math;
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/figure_types.dart';
import 'package:chess_app/models/game_colors.dart';

/// Advanced AI with position evaluation and strategic thinking
class AdvancedAI {
  final math.Random _random = math.Random();
  final int difficulty;

  AdvancedAI({required this.difficulty});

  /// Get the best move using minimax algorithm with position evaluation
  Future<String?> getBestMove(Board board, GameColors color) async {
    // Simulate thinking time based on difficulty
    final thinkingTime = 300 + (difficulty * 400) + _random.nextInt(500);
    await Future.delayed(Duration(milliseconds: thinkingTime));

    final validMoves = _getAllValidMoves(board, color);

    if (validMoves.isEmpty) {
      return null;
    }

    // Use different strategies based on difficulty
    switch (difficulty) {
      case 1:
        return _getBasicMove(board, color, validMoves);
      case 2:
        return _getTacticalMove(board, color, validMoves);
      case 3:
        return _getStrategicMove(board, color, validMoves);
      case 4:
        return _getMasterMove(board, color, validMoves);
      case 5:
        return _getGrandmasterMove(board, color, validMoves);
      default:
        return _getGrandmasterMove(board, color, validMoves);
    }
  }

  String _getBasicMove(Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = 0.0;

      // Prioritize captures
      if (_isCapture(board, move)) {
        final capturedPiece = _getCapturedPieceType(board, move);
        score += _getPieceValue(capturedPiece) * 5;
      }

      // Add randomness
      score += _random.nextDouble() * 3;

      moveScores[move] = score;
    }

    final bestMove = moveScores.entries.reduce((a, b) => a.value > b.value ? a : b);
    return bestMove.key;
  }

  String _getTacticalMove(Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = 0.0;

      // Prioritize captures
      if (_isCapture(board, move)) {
        final capturedPiece = _getCapturedPieceType(board, move);
        score += _getPieceValue(capturedPiece) * 10;
      }

      // Prioritize center control
      final toPos = _uciToPosition(move.substring(2, 4));
      if (toPos != null) {
        score += _getCenterControlScore(toPos['x']!, toPos['y']!);
      }

      // Pawn promotion bonus
      if (move.length == 5 && move[4] == 'q') {
        score += 50;
      }

      // Add randomness
      score += _random.nextDouble() * 2;

      moveScores[move] = score;
    }

    final bestMove = moveScores.entries.reduce((a, b) => a.value > b.value ? a : b);
    return bestMove.key;
  }

  String _getStrategicMove(Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = _evaluateMove(board, move, color);
      moveScores[move] = score;
    }

    // Get top 3 moves and add some randomness
    final sortedMoves = moveScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topMoves = sortedMoves.take(3).toList();
    final weights = [0.6, 0.3, 0.1];

    final randomValue = _random.nextDouble();
    double cumulative = 0.0;

    for (int i = 0; i < topMoves.length; i++) {
      cumulative += weights[i];
      if (randomValue <= cumulative) {
        return topMoves[i].key;
      }
    }

    return topMoves.first.key;
  }

  String _getMasterMove(Board board, GameColors color, List<String> validMoves) {
    if (validMoves.isEmpty) return '';

    // Use minimax algorithm for master level
    final bestMove = _minimaxRoot(board, color, 3);
    return bestMove.isNotEmpty ? bestMove : validMoves.first;
  }

  String _getGrandmasterMove(Board board, GameColors color, List<String> validMoves) {
    if (validMoves.isEmpty) return '';

    // Use deeper minimax algorithm for grandmaster level
    final bestMove = _minimaxRoot(board, color, 4);
    return bestMove.isNotEmpty ? bestMove : validMoves.first;
  }

  // Minimax algorithm with alpha-beta pruning
  String _minimaxRoot(Board board, GameColors color, int depth) {
    String bestMove = '';
    double bestValue = double.negativeInfinity;
    final validMoves = _getAllValidMoves(board, color);

    for (final move in validMoves) {
      final boardCopy = _simulateMove(board, move);
      if (boardCopy != null) {
        final value = _minimax(boardCopy, depth - 1, double.negativeInfinity, 
                              double.infinity, false, color);
        if (value > bestValue) {
          bestValue = value;
          bestMove = move;
        }
      }
    }

    return bestMove;
  }

  double _minimax(Board board, int depth, double alpha, double beta,
      bool isMaximizing, GameColors originalColor) {
    if (depth == 0) {
      return _evaluateBoardPosition(board, originalColor);
    }

    final currentColor = isMaximizing
        ? originalColor
        : (originalColor == GameColors.white
            ? GameColors.black
            : GameColors.white);
    final validMoves = _getAllValidMoves(board, currentColor);

    if (validMoves.isEmpty) {
      // No moves available - check if it's checkmate or stalemate
      return isMaximizing ? -9999 : 9999;
    }

    if (isMaximizing) {
      double maxEval = double.negativeInfinity;
      for (final move in validMoves) {
        final boardCopy = _simulateMove(board, move);
        if (boardCopy != null) {
          final eval =
              _minimax(boardCopy, depth - 1, alpha, beta, false, originalColor);
          maxEval = math.max(maxEval, eval);
          alpha = math.max(alpha, eval);
          if (beta <= alpha) break; // Alpha-beta pruning
        }
      }
      return maxEval;
    } else {
      double minEval = double.infinity;
      for (final move in validMoves) {
        final boardCopy = _simulateMove(board, move);
        if (boardCopy != null) {
          final eval =
              _minimax(boardCopy, depth - 1, alpha, beta, true, originalColor);
          minEval = math.min(minEval, eval);
          beta = math.min(beta, eval);
          if (beta <= alpha) break; // Alpha-beta pruning
        }
      }
      return minEval;
    }
  }

  double _evaluateMove(Board board, String move, GameColors color) {
    double score = 0.0;

    // Capture evaluation with exchange analysis
    if (_isCapture(board, move)) {
      final capturedPiece = _getCapturedPieceType(board, move);
      final capturingPiece = _getMovingPieceType(board, move);

      final capturedValue = _getPieceValue(capturedPiece);
      final capturingValue = _getPieceValue(capturingPiece);

      // Good captures: taking higher value piece with lower value piece
      if (capturedValue >= capturingValue) {
        score += capturedValue * 20; // Excellent capture
      } else {
        score += (capturedValue - capturingValue * 0.5) * 10; // Risky capture
      }

      // King capture is ultimate goal
      if (capturedPiece == FigureTypes.king) {
        score += 10000; // Winning move
      }
    }

    // Pawn promotion bonus
    if (move.length == 5 && move[4] == 'q') {
      score += 800; // Huge bonus for pawn promotion to queen
    }

    // Enhanced center control with piece-specific bonuses
    final toPos = _uciToPosition(move.substring(2, 4));
    final fromPos = _uciToPosition(move.substring(0, 2));

    if (toPos != null && fromPos != null) {
      final fromCell = board.getCellAt(fromPos['x']!, fromPos['y']!);
      if (fromCell.occupied) {
        final piece = fromCell.getFigure()!;
        final centerScore = _getCenterControlScore(toPos['x']!, toPos['y']!);

        // Different pieces benefit differently from center control
        switch (piece.type) {
          case FigureTypes.pawn:
            score += centerScore * 1.5;
            break;
          case FigureTypes.knight:
            score += centerScore * 3.0; // Knights love the center
            break;
          case FigureTypes.bishop:
            score += centerScore * 2.0;
            break;
          case FigureTypes.rook:
            score += centerScore * 1.0;
            break;
          case FigureTypes.queen:
            score += centerScore * 2.5;
            break;
          case FigureTypes.king:
            score += centerScore * 0.5; // King should avoid center early
            break;
        }
      }
    }

    return score;
  }

  bool _isCapture(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return false;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied;
  }

  FigureTypes? _getCapturedPieceType(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return null;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied ? toCell.getFigure()!.type : null;
  }

  FigureTypes? _getMovingPieceType(Board board, String move) {
    final fromPos = _uciToPosition(move.substring(0, 2));
    if (fromPos == null) return null;

    final fromCell = board.getCellAt(fromPos['x']!, fromPos['y']!);
    return fromCell.occupied ? fromCell.getFigure()!.type : null;
  }

  double _getPieceValue(FigureTypes? type) {
    switch (type) {
      case FigureTypes.pawn:
        return 1.0;
      case FigureTypes.knight:
        return 3.2; // Knights slightly more valuable
      case FigureTypes.bishop:
        return 3.3; // Bishops slightly more valuable than knights
      case FigureTypes.rook:
        return 5.0;
      case FigureTypes.queen:
        return 9.0;
      case FigureTypes.king:
        return 1000.0; // Extremely high value for king capture
      default:
        return 0.0;
    }
  }

  double _getCenterControlScore(int x, int y) {
    // Higher score for center squares
    final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
    return 4.0 - centerDistance;
  }

  List<String> _getAllValidMoves(Board board, GameColors color) {
    final List<String> moves = [];

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied && cell.getFigure()!.color == color) {
          final pieceMoves = _getValidMovesForPiece(board, cell);
          moves.addAll(pieceMoves);
        }
      }
    }

    return moves;
  }

  List<String> _getValidMovesForPiece(Board board, Cell fromCell) {
    final List<String> moves = [];
    final figure = fromCell.getFigure()!;

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final toCell = board.getCellAt(x, y);

        if (figure.availableForMove(toCell)) {
          final fromUci =
              _positionToUci(fromCell.position.x, fromCell.position.y);
          final toUci = _positionToUci(x, y);

          // Check for pawn promotion
          if (figure.type == FigureTypes.pawn) {
            final isWhitePawnPromotion =
                figure.color == GameColors.white && y == 0;
            final isBlackPawnPromotion =
                figure.color == GameColors.black && y == 7;

            if (isWhitePawnPromotion || isBlackPawnPromotion) {
              // Add promotion to queen (most valuable piece)
              moves.add('$fromUci${toUci}q');
            } else {
              moves.add('$fromUci$toUci');
            }
          } else {
            moves.add('$fromUci$toUci');
          }
        }
      }
    }

    return moves;
  }

  Map<String, int>? _uciToPosition(String uciSquare) {
    if (uciSquare.length != 2) return null;

    final file = uciSquare[0].toLowerCase();
    final rank = uciSquare[1];

    final x = file.codeUnitAt(0) - 'a'.codeUnitAt(0);
    final y = 8 - int.parse(rank);

    if (x < 0 || x >= 8 || y < 0 || y >= 8) return null;

    return {'x': x, 'y': y};
  }

  String _positionToUci(int x, int y) {
    final file = String.fromCharCode('a'.codeUnitAt(0) + x);
    final rank = (8 - y).toString();
    return '$file$rank';
  }

  // Board simulation for minimax
  Board? _simulateMove(Board board, String move) {
    try {
      // Create a deep copy of the board
      final boardCopy = board.copyThis();

      // Make the move on the copy
      final success = boardCopy.makeUCIMove(move);
      return success ? boardCopy : null;
    } catch (e) {
      return null;
    }
  }

  // Comprehensive board position evaluation
  double _evaluateBoardPosition(Board board, GameColors color) {
    double score = 0.0;

    // Material evaluation
    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied) {
          final piece = cell.getFigure()!;
          final value = _getPieceValue(piece.type);

          if (piece.color == color) {
            score += value;
          } else {
            score -= value;
          }
        }
      }
    }

    return score;
  }
}
