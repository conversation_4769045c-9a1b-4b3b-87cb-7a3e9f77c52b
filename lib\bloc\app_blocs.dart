import 'package:chess_app/bloc/cubits/game_cubit.dart';
import 'package:chess_app/bloc/cubits/settings_cubit.dart';
import 'package:chess_app/services/ai/ai_manager.dart';
import 'package:get_it/get_it.dart';

createAppBlocs() {
  GetIt.I.registerSingleton<SettingsCubit>(SettingsCubit.initial());
  // Use simple AI to avoid Stockfish issues on Windows
  GetIt.I.registerSingleton<GameCubit>(
      GameCubit.initial(aiEngine: AIEngines.simple));
}
