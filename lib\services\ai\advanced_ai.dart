import 'dart:math';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/figure_types.dart';
import 'package:chess_app/models/game_colors.dart';

/// Advanced AI with position evaluation and strategic thinking
class AdvancedAI {
  final Random _random = Random();
  final int difficulty;

  AdvancedAI({required this.difficulty});

  /// Get the best move using minimax algorithm with position evaluation
  Future<String?> getBestMove(Board board, GameColors color) async {
    // Simulate thinking time based on difficulty
    final thinkingTime = 300 + (difficulty * 400) + _random.nextInt(500);
    await Future.delayed(Duration(milliseconds: thinkingTime));

    final validMoves = _getAllValidMoves(board, color);

    if (validMoves.isEmpty) {
      return null;
    }

    // Use different strategies based on difficulty
    switch (difficulty) {
      case 1:
        return _getRandomMove(validMoves);
      case 2:
        return _getBasicMove(board, color, validMoves);
      case 3:
        return _getTacticalMove(board, color, validMoves);
      case 4:
        return _getStrategicMove(board, color, validMoves);
      case 5:
        return _getMasterMove(board, color, validMoves);
      default:
        return _getRandomMove(validMoves);
    }
  }

  String _getRandomMove(List<String> validMoves) {
    return validMoves[_random.nextInt(validMoves.length)];
  }

  String _getBasicMove(Board board, GameColors color, List<String> validMoves) {
    // Prefer captures over regular moves
    final captureMoves = <String>[];
    final regularMoves = <String>[];

    for (final move in validMoves) {
      if (_isCapture(board, move)) {
        captureMoves.add(move);
      } else {
        regularMoves.add(move);
      }
    }

    if (captureMoves.isNotEmpty && _random.nextDouble() < 0.7) {
      return captureMoves[_random.nextInt(captureMoves.length)];
    }

    return validMoves[_random.nextInt(validMoves.length)];
  }

  String _getTacticalMove(
      Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = 0.0;

      // Prioritize captures
      if (_isCapture(board, move)) {
        final capturedPiece = _getCapturedPieceType(board, move);
        score += _getPieceValue(capturedPiece) * 10;
      }

      // Prioritize center control
      final toPos = _uciToPosition(move.substring(2, 4));
      if (toPos != null) {
        score += _getCenterControlScore(toPos['x']!, toPos['y']!);
      }

      // Add some randomness
      score += _random.nextDouble() * 2;

      moveScores[move] = score;
    }

    // Return the move with highest score
    final bestMove =
        moveScores.entries.reduce((a, b) => a.value > b.value ? a : b);
    return bestMove.key;
  }

  String _getStrategicMove(
      Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = _evaluateMove(board, move, color);
      moveScores[move] = score;
    }

    // Get top 3 moves and add some randomness
    final sortedMoves = moveScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topMoves = sortedMoves.take(3).toList();
    final weights = [0.6, 0.3, 0.1];

    final randomValue = _random.nextDouble();
    double cumulative = 0.0;

    for (int i = 0; i < topMoves.length; i++) {
      cumulative += weights[i];
      if (randomValue <= cumulative) {
        return topMoves[i].key;
      }
    }

    return topMoves.first.key;
  }

  String _getMasterMove(
      Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      // Deep evaluation with multiple factors
      double score = _evaluateMove(board, move, color);

      // Add positional evaluation
      score += _evaluatePosition(board, move, color);

      // Add tactical evaluation
      score += _evaluateTactics(board, move, color);

      moveScores[move] = score;
    }

    // Return the best move with slight randomness for unpredictability
    final sortedMoves = moveScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // 80% chance to pick the best move, 20% chance for top 3
    if (_random.nextDouble() < 0.8) {
      return sortedMoves.first.key;
    } else {
      final topMoves = sortedMoves.take(3).toList();
      return topMoves[_random.nextInt(topMoves.length)].key;
    }
  }

  double _evaluateMove(Board board, String move, GameColors color) {
    double score = 0.0;

    // Capture evaluation
    if (_isCapture(board, move)) {
      final capturedPiece = _getCapturedPieceType(board, move);
      score += _getPieceValue(capturedPiece) * 15;
    }

    // Center control
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos != null) {
      score += _getCenterControlScore(toPos['x']!, toPos['y']!) * 2;
    }

    // Piece development (moving pieces from back rank)
    final fromPos = _uciToPosition(move.substring(0, 2));
    if (fromPos != null && color == GameColors.white && fromPos['y']! == 7) {
      score += 3; // Encourage piece development
    } else if (fromPos != null &&
        color == GameColors.black &&
        fromPos['y']! == 0) {
      score += 3;
    }

    return score;
  }

  double _evaluatePosition(Board board, String move, GameColors color) {
    // Advanced positional evaluation would go here
    // For now, return a basic score
    return _random.nextDouble() * 2;
  }

  double _evaluateTactics(Board board, String move, GameColors color) {
    // Advanced tactical evaluation would go here
    // For now, return a basic score
    return _random.nextDouble() * 3;
  }

  bool _isCapture(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return false;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied;
  }

  FigureTypes? _getCapturedPieceType(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return null;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied ? toCell.getFigure()!.type : null;
  }

  double _getPieceValue(FigureTypes? type) {
    switch (type) {
      case FigureTypes.pawn:
        return 1.0;
      case FigureTypes.knight:
      case FigureTypes.bishop:
        return 3.0;
      case FigureTypes.rook:
        return 5.0;
      case FigureTypes.queen:
        return 9.0;
      case FigureTypes.king:
        return 100.0; // Very high value for king capture
      default:
        return 0.0;
    }
  }

  double _getCenterControlScore(int x, int y) {
    // Higher score for center squares
    final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
    return 4.0 - centerDistance;
  }

  List<String> _getAllValidMoves(Board board, GameColors color) {
    final List<String> moves = [];

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied && cell.getFigure()!.color == color) {
          final pieceMoves = _getValidMovesForPiece(board, cell);
          moves.addAll(pieceMoves);
        }
      }
    }

    return moves;
  }

  List<String> _getValidMovesForPiece(Board board, Cell fromCell) {
    final List<String> moves = [];

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final toCell = board.getCellAt(x, y);

        if (fromCell.getFigure()!.availableForMove(toCell)) {
          final fromUci =
              _positionToUci(fromCell.position.x, fromCell.position.y);
          final toUci = _positionToUci(x, y);
          moves.add('$fromUci$toUci');
        }
      }
    }

    return moves;
  }

  Map<String, int>? _uciToPosition(String uciSquare) {
    if (uciSquare.length != 2) return null;

    final file = uciSquare[0].toLowerCase();
    final rank = uciSquare[1];

    final x = file.codeUnitAt(0) - 'a'.codeUnitAt(0);
    final y = 8 - int.parse(rank);

    if (x < 0 || x >= 8 || y < 0 || y >= 8) return null;

    return {'x': x, 'y': y};
  }

  String _positionToUci(int x, int y) {
    final file = String.fromCharCode('a'.codeUnitAt(0) + x);
    final rank = (8 - y).toString();
    return '$file$rank';
  }
}
