import 'dart:math' as math;
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/figure_types.dart';
import 'package:chess_app/models/game_colors.dart';

/// Advanced AI with position evaluation and strategic thinking
class AdvancedAI {
  final math.Random _random = math.Random();
  final int difficulty;

  AdvancedAI({required this.difficulty});

  /// Get the best move using minimax algorithm with position evaluation
  Future<String?> getBestMove(Board board, GameColors color) async {
    // Simulate thinking time based on difficulty
    final thinkingTime = 300 + (difficulty * 400) + _random.nextInt(500);
    await Future.delayed(Duration(milliseconds: thinkingTime));

    final validMoves = _getAllValidMoves(board, color);

    if (validMoves.isEmpty) {
      return null;
    }

    // Use different strategies based on difficulty
    switch (difficulty) {
      case 1:
        return _getBasicMove(board, color, validMoves); // Upgraded from random
      case 2:
        return _getTacticalMove(
            board, color, validMoves); // Upgraded from basic
      case 3:
        return _getStrategicMove(
            board, color, validMoves); // Upgraded from tactical
      case 4:
        return _getMasterMove(
            board, color, validMoves); // Upgraded from strategic
      case 5:
        return _getGrandmasterMove(
            board, color, validMoves); // New highest level
      default:
        return _getGrandmasterMove(board, color, validMoves);
    }
  }

  String _getRandomMove(List<String> validMoves) {
    return validMoves[_random.nextInt(validMoves.length)];
  }

  String _getBasicMove(Board board, GameColors color, List<String> validMoves) {
    // Prefer captures over regular moves
    final captureMoves = <String>[];
    final regularMoves = <String>[];

    for (final move in validMoves) {
      if (_isCapture(board, move)) {
        captureMoves.add(move);
      } else {
        regularMoves.add(move);
      }
    }

    if (captureMoves.isNotEmpty && _random.nextDouble() < 0.7) {
      return captureMoves[_random.nextInt(captureMoves.length)];
    }

    return validMoves[_random.nextInt(validMoves.length)];
  }

  String _getTacticalMove(
      Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = 0.0;

      // Prioritize captures
      if (_isCapture(board, move)) {
        final capturedPiece = _getCapturedPieceType(board, move);
        score += _getPieceValue(capturedPiece) * 10;
      }

      // Prioritize center control
      final toPos = _uciToPosition(move.substring(2, 4));
      if (toPos != null) {
        score += _getCenterControlScore(toPos['x']!, toPos['y']!);
      }

      // Add some randomness
      score += _random.nextDouble() * 2;

      moveScores[move] = score;
    }

    // Return the move with highest score
    final bestMove =
        moveScores.entries.reduce((a, b) => a.value > b.value ? a : b);
    return bestMove.key;
  }

  String _getStrategicMove(
      Board board, GameColors color, List<String> validMoves) {
    final moveScores = <String, double>{};

    for (final move in validMoves) {
      double score = _evaluateMove(board, move, color);
      moveScores[move] = score;
    }

    // Get top 3 moves and add some randomness
    final sortedMoves = moveScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topMoves = sortedMoves.take(3).toList();
    final weights = [0.6, 0.3, 0.1];

    final randomValue = _random.nextDouble();
    double cumulative = 0.0;

    for (int i = 0; i < topMoves.length; i++) {
      cumulative += weights[i];
      if (randomValue <= cumulative) {
        return topMoves[i].key;
      }
    }

    return topMoves.first.key;
  }

  String _getMasterMove(
      Board board, GameColors color, List<String> validMoves) {
    if (validMoves.isEmpty) return '';

    // Use minimax algorithm for master level
    final bestMove = _minimaxRoot(board, color, 3); // Depth 3 for master level
    return bestMove.isNotEmpty ? bestMove : validMoves.first;
  }

  String _getGrandmasterMove(
      Board board, GameColors color, List<String> validMoves) {
    if (validMoves.isEmpty) return '';

    // Use deeper minimax algorithm for grandmaster level
    final bestMove =
        _minimaxRoot(board, color, 5); // Depth 5 for grandmaster level
    return bestMove.isNotEmpty ? bestMove : validMoves.first;
  }

  // Minimax algorithm with alpha-beta pruning
  String _minimaxRoot(Board board, GameColors color, int depth) {
    String bestMove = '';
    double bestValue = double.negativeInfinity;
    final validMoves = _getAllValidMoves(board, color);

    for (final move in validMoves) {
      final boardCopy = _simulateMove(board, move);
      if (boardCopy != null) {
        final value = _minimax(boardCopy, depth - 1, double.negativeInfinity,
            double.infinity, false, color);
        if (value > bestValue) {
          bestValue = value;
          bestMove = move;
        }
      }
    }

    return bestMove;
  }

  double _minimax(Board board, int depth, double alpha, double beta,
      bool isMaximizing, GameColors originalColor) {
    if (depth == 0) {
      return _evaluateBoardPosition(board, originalColor);
    }

    final currentColor = isMaximizing
        ? originalColor
        : (originalColor == GameColors.white
            ? GameColors.black
            : GameColors.white);
    final validMoves = _getAllValidMoves(board, currentColor);

    if (validMoves.isEmpty) {
      // No moves available - check if it's checkmate or stalemate
      return isMaximizing ? -9999 : 9999;
    }

    if (isMaximizing) {
      double maxEval = double.negativeInfinity;
      for (final move in validMoves) {
        final boardCopy = _simulateMove(board, move);
        if (boardCopy != null) {
          final eval =
              _minimax(boardCopy, depth - 1, alpha, beta, false, originalColor);
          maxEval = math.max(maxEval, eval);
          alpha = math.max(alpha, eval);
          if (beta <= alpha) break; // Alpha-beta pruning
        }
      }
      return maxEval;
    } else {
      double minEval = double.infinity;
      for (final move in validMoves) {
        final boardCopy = _simulateMove(board, move);
        if (boardCopy != null) {
          final eval =
              _minimax(boardCopy, depth - 1, alpha, beta, true, originalColor);
          minEval = math.min(minEval, eval);
          beta = math.min(beta, eval);
          if (beta <= alpha) break; // Alpha-beta pruning
        }
      }
      return minEval;
    }
  }

  double _evaluateMove(Board board, String move, GameColors color) {
    double score = 0.0;

    // Capture evaluation with exchange analysis
    if (_isCapture(board, move)) {
      final capturedPiece = _getCapturedPieceType(board, move);
      final capturingPiece = _getMovingPieceType(board, move);

      final capturedValue = _getPieceValue(capturedPiece);
      final capturingValue = _getPieceValue(capturingPiece);

      // Good captures: taking higher value piece with lower value piece
      if (capturedValue >= capturingValue) {
        score += capturedValue * 20; // Excellent capture
      } else {
        score += (capturedValue - capturingValue * 0.5) * 10; // Risky capture
      }

      // King capture is ultimate goal
      if (capturedPiece == FigureTypes.king) {
        score += 10000; // Winning move
      }
    }

    // Pawn promotion bonus
    if (move.length == 5 && move[4] == 'q') {
      score += 800; // Huge bonus for pawn promotion to queen
    }

    // Enhanced center control with piece-specific bonuses
    final toPos = _uciToPosition(move.substring(2, 4));
    final fromPos = _uciToPosition(move.substring(0, 2));

    if (toPos != null && fromPos != null) {
      final fromCell = board.getCellAt(fromPos['x']!, fromPos['y']!);
      if (fromCell.occupied) {
        final piece = fromCell.getFigure()!;
        final centerScore = _getCenterControlScore(toPos['x']!, toPos['y']!);

        // Different pieces benefit differently from center control
        switch (piece.type) {
          case FigureTypes.pawn:
            score += centerScore * 1.5;
            break;
          case FigureTypes.knight:
            score += centerScore * 3.0; // Knights love the center
            break;
          case FigureTypes.bishop:
            score += centerScore * 2.0;
            break;
          case FigureTypes.rook:
            score += centerScore * 1.0;
            break;
          case FigureTypes.queen:
            score += centerScore * 2.5;
            break;
          case FigureTypes.king:
            score += centerScore * 0.5; // King should avoid center early
            break;
        }
      }
    }

    // Enhanced piece development
    if (fromPos != null) {
      final isBackRank = (color == GameColors.white && fromPos['y']! == 7) ||
          (color == GameColors.black && fromPos['y']! == 0);
      if (isBackRank) {
        score += 4; // Encourage piece development
      }

      // Discourage moving the same piece multiple times early
      final isSecondRank = (color == GameColors.white && fromPos['y']! == 6) ||
          (color == GameColors.black && fromPos['y']! == 1);
      if (isSecondRank) {
        score -= 1; // Slight penalty for moving developed pieces again
      }
    }

    // Threat evaluation - check if this move creates threats
    score += _evaluateThreats(board, move, color);

    return score;
  }

  double _evaluatePosition(Board board, String move, GameColors color) {
    double score = 0.0;

    final fromPos = _uciToPosition(move.substring(0, 2));
    final toPos = _uciToPosition(move.substring(2, 4));

    if (fromPos == null || toPos == null) return 0.0;

    final fromCell = board.getCellAt(fromPos['x']!, fromPos['y']!);
    if (!fromCell.occupied) return 0.0;

    final piece = fromCell.getFigure()!;

    // Piece-specific positional bonuses
    switch (piece.type) {
      case FigureTypes.pawn:
        // Pawns advance towards promotion
        if (color == GameColors.white) {
          score += (7 - toPos['y']!) * 0.5; // Closer to 8th rank = better
        } else {
          score += toPos['y']! * 0.5; // Closer to 1st rank = better
        }
        break;
      case FigureTypes.knight:
        // Knights prefer center squares
        score += _getCenterControlScore(toPos['x']!, toPos['y']!) * 1.5;
        break;
      case FigureTypes.bishop:
        // Bishops prefer long diagonals
        score += _getDiagonalControlScore(toPos['x']!, toPos['y']!);
        break;
      case FigureTypes.rook:
        // Rooks prefer open files and ranks
        score += _getFileRankControlScore(board, toPos['x']!, toPos['y']!);
        break;
      case FigureTypes.queen:
        // Queen prefers central control but not too early
        score += _getCenterControlScore(toPos['x']!, toPos['y']!) * 0.8;
        break;
      case FigureTypes.king:
        // King safety evaluation
        score += _evaluateKingSafety(board, toPos['x']!, toPos['y']!, color);
        break;
    }

    return score;
  }

  double _evaluateTactics(Board board, String move, GameColors color) {
    double score = 0.0;

    // Check for forks, pins, skewers, etc.
    score += _evaluateForksAndPins(board, move, color);

    // Check for discovered attacks
    score += _evaluateDiscoveredAttacks(board, move, color);

    // Check for defensive moves
    score += _evaluateDefensiveMoves(board, move, color);

    return score;
  }

  double _evaluateAdvancedStrategy(Board board, String move, GameColors color) {
    double score = 0.0;

    // Evaluate pawn structure
    score += _evaluatePawnStructure(board, move, color);

    // Evaluate piece coordination
    score += _evaluatePieceCoordination(board, move, color);

    // Evaluate space advantage
    score += _evaluateSpaceAdvantage(board, move, color);

    // Evaluate tempo and initiative
    score += _evaluateTempoAndInitiative(board, move, color);

    return score;
  }

  bool _isCapture(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return false;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied;
  }

  FigureTypes? _getCapturedPieceType(Board board, String move) {
    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return null;

    final toCell = board.getCellAt(toPos['x']!, toPos['y']!);
    return toCell.occupied ? toCell.getFigure()!.type : null;
  }

  double _getPieceValue(FigureTypes? type) {
    switch (type) {
      case FigureTypes.pawn:
        return 1.0;
      case FigureTypes.knight:
        return 3.2; // Knights slightly more valuable
      case FigureTypes.bishop:
        return 3.3; // Bishops slightly more valuable than knights
      case FigureTypes.rook:
        return 5.0;
      case FigureTypes.queen:
        return 9.0;
      case FigureTypes.king:
        return 1000.0; // Extremely high value for king capture
      default:
        return 0.0;
    }
  }

  double _getCenterControlScore(int x, int y) {
    // Higher score for center squares
    final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
    return 4.0 - centerDistance;
  }

  List<String> _getAllValidMoves(Board board, GameColors color) {
    final List<String> moves = [];

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied && cell.getFigure()!.color == color) {
          final pieceMoves = _getValidMovesForPiece(board, cell);
          moves.addAll(pieceMoves);
        }
      }
    }

    return moves;
  }

  List<String> _getValidMovesForPiece(Board board, Cell fromCell) {
    final List<String> moves = [];
    final figure = fromCell.getFigure()!;

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final toCell = board.getCellAt(x, y);

        if (figure.availableForMove(toCell)) {
          final fromUci =
              _positionToUci(fromCell.position.x, fromCell.position.y);
          final toUci = _positionToUci(x, y);

          // Check for pawn promotion
          if (figure.type == FigureTypes.pawn) {
            final isWhitePawnPromotion =
                figure.color == GameColors.white && y == 0;
            final isBlackPawnPromotion =
                figure.color == GameColors.black && y == 7;

            if (isWhitePawnPromotion || isBlackPawnPromotion) {
              // Add promotion to queen (most valuable piece)
              moves.add('$fromUci${toUci}q');
            } else {
              moves.add('$fromUci$toUci');
            }
          } else {
            moves.add('$fromUci$toUci');
          }
        }
      }
    }

    return moves;
  }

  Map<String, int>? _uciToPosition(String uciSquare) {
    if (uciSquare.length != 2) return null;

    final file = uciSquare[0].toLowerCase();
    final rank = uciSquare[1];

    final x = file.codeUnitAt(0) - 'a'.codeUnitAt(0);
    final y = 8 - int.parse(rank);

    if (x < 0 || x >= 8 || y < 0 || y >= 8) return null;

    return {'x': x, 'y': y};
  }

  String _positionToUci(int x, int y) {
    final file = String.fromCharCode('a'.codeUnitAt(0) + x);
    final rank = (8 - y).toString();
    return '$file$rank';
  }

  // Additional helper methods for advanced evaluation
  double _getDiagonalControlScore(int x, int y) {
    // Score based on diagonal length from this position
    double score = 0.0;

    // Main diagonal (a1-h8)
    if (x == y) score += 2.0;

    // Anti-diagonal (a8-h1)
    if (x + y == 7) score += 2.0;

    // Long diagonals get bonus
    final minDistToEdge = [x, y, 7 - x, 7 - y].reduce((a, b) => a < b ? a : b);
    score += minDistToEdge * 0.3;

    return score;
  }

  double _getFileRankControlScore(Board board, int x, int y) {
    double score = 0.0;

    // Check if file is open (no pawns)
    bool fileOpen = true;
    for (int checkY = 0; checkY < 8; checkY++) {
      final cell = board.getCellAt(x, checkY);
      if (cell.occupied && cell.getFigure()!.type == FigureTypes.pawn) {
        fileOpen = false;
        break;
      }
    }
    if (fileOpen) score += 3.0;

    // Check if rank is open
    bool rankOpen = true;
    for (int checkX = 0; checkX < 8; checkX++) {
      final cell = board.getCellAt(checkX, y);
      if (cell.occupied && cell.getFigure()!.type == FigureTypes.pawn) {
        rankOpen = false;
        break;
      }
    }
    if (rankOpen) score += 2.0;

    return score;
  }

  double _evaluateKingSafety(Board board, int x, int y, GameColors color) {
    double score = 0.0;

    // King should stay away from center in opening/middlegame
    final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
    score += centerDistance * 1.5;

    // King should have pawn shield
    final pawnShieldScore = _evaluatePawnShield(board, x, y, color);
    score += pawnShieldScore;

    return score;
  }

  double _evaluatePawnShield(
      Board board, int kingX, int kingY, GameColors color) {
    double score = 0.0;
    final direction = color == GameColors.white ? -1 : 1;

    // Check pawns in front of king
    for (int dx = -1; dx <= 1; dx++) {
      final checkX = kingX + dx;
      final checkY = kingY + direction;

      if (checkX >= 0 && checkX < 8 && checkY >= 0 && checkY < 8) {
        final cell = board.getCellAt(checkX, checkY);
        if (cell.occupied &&
            cell.getFigure()!.type == FigureTypes.pawn &&
            cell.getFigure()!.color == color) {
          score += 2.0;
        }
      }
    }

    return score;
  }

  double _evaluateForksAndPins(Board board, String move, GameColors color) {
    // Simplified tactical evaluation
    return _random.nextDouble() * 2.0;
  }

  double _evaluateDiscoveredAttacks(
      Board board, String move, GameColors color) {
    // Simplified discovered attack evaluation
    return _random.nextDouble() * 1.5;
  }

  double _evaluateDefensiveMoves(Board board, String move, GameColors color) {
    // Simplified defensive evaluation
    return _random.nextDouble() * 1.0;
  }

  double _evaluatePawnStructure(Board board, String move, GameColors color) {
    // Simplified pawn structure evaluation
    return _random.nextDouble() * 1.5;
  }

  double _evaluatePieceCoordination(
      Board board, String move, GameColors color) {
    // Simplified piece coordination evaluation
    return _random.nextDouble() * 2.0;
  }

  double _evaluateSpaceAdvantage(Board board, String move, GameColors color) {
    // Simplified space evaluation
    return _random.nextDouble() * 1.0;
  }

  double _evaluateTempoAndInitiative(
      Board board, String move, GameColors color) {
    // Simplified tempo evaluation
    return _random.nextDouble() * 1.5;
  }

  FigureTypes? _getMovingPieceType(Board board, String move) {
    final fromPos = _uciToPosition(move.substring(0, 2));
    if (fromPos == null) return null;

    final fromCell = board.getCellAt(fromPos['x']!, fromPos['y']!);
    return fromCell.occupied ? fromCell.getFigure()!.type : null;
  }

  double _evaluateThreats(Board board, String move, GameColors color) {
    double score = 0.0;

    final toPos = _uciToPosition(move.substring(2, 4));
    if (toPos == null) return 0.0;

    // Check if this move attacks enemy pieces
    final enemyColor =
        color == GameColors.white ? GameColors.black : GameColors.white;

    // Simple threat evaluation - check adjacent squares for enemy pieces
    for (int dx = -1; dx <= 1; dx++) {
      for (int dy = -1; dy <= 1; dy++) {
        if (dx == 0 && dy == 0) continue;

        final checkX = toPos['x']! + dx;
        final checkY = toPos['y']! + dy;

        if (checkX >= 0 && checkX < 8 && checkY >= 0 && checkY < 8) {
          final cell = board.getCellAt(checkX, checkY);
          if (cell.occupied && cell.getFigure()!.color == enemyColor) {
            // This move threatens an enemy piece
            score += _getPieceValue(cell.getFigure()!.type) * 0.5;
          }
        }
      }
    }

    return score;
  }

  // Board simulation for minimax
  Board? _simulateMove(Board board, String move) {
    try {
      // Create a deep copy of the board
      final boardCopy = board.copyThis();

      // Make the move on the copy
      final success = boardCopy.makeUCIMove(move);
      return success ? boardCopy : null;
    } catch (e) {
      return null;
    }
  }

  // Comprehensive board position evaluation
  double _evaluateBoardPosition(Board board, GameColors color) {
    double score = 0.0;

    // Material evaluation
    score += _evaluateMaterial(board, color);

    // Positional evaluation
    score += _evaluateAllPositions(board, color);

    // King safety
    score += _evaluateKingSafetyForBoard(board, color);

    // Pawn structure
    score += _evaluatePawnStructureForBoard(board, color);

    return score;
  }

  double _evaluateMaterial(Board board, GameColors color) {
    double score = 0.0;

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied) {
          final piece = cell.getFigure()!;
          final value = _getPieceValue(piece.type);

          if (piece.color == color) {
            score += value;
            // Bonus for piece activity and positioning
            score += _getAdvancedPositionalBonus(piece.type, x, y, color);
          } else {
            score -= value;
            // Penalty for enemy piece activity
            score -= _getAdvancedPositionalBonus(piece.type, x, y, piece.color) * 0.5;
          }
        }
      }
    }

    return score;
  }

  double _getAdvancedPositionalBonus(FigureTypes type, int x, int y, GameColors color) {
    double bonus = 0.0;

    switch (type) {
      case FigureTypes.pawn:
        // Advanced pawn evaluation
        if (color == GameColors.white) {
          bonus += (7 - y) * 0.8; // Closer to promotion
          if (y <= 3) bonus += 2.0; // Advanced pawns
        } else {
          bonus += y * 0.8;
          if (y >= 4) bonus += 2.0;
        }

        // Center pawns bonus
        if (x >= 2 && x <= 5) bonus += 1.0;
        break;

      case FigureTypes.knight:
        // Knights prefer center and outposts
        final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
        bonus += (4 - centerDistance) * 0.8;

        // Outpost bonus (protected advanced squares)
        if ((color == GameColors.white && y <= 4) ||
            (color == GameColors.black && y >= 3)) {
          bonus += 1.5;
        }
        break;

      case FigureTypes.bishop:
        // Bishop pair bonus and long diagonal control
        bonus += _getDiagonalControlScore(x, y) * 0.6;

        // Penalty for blocked bishops
        if ((color == GameColors.white && y == 7) ||
            (color == GameColors.black && y == 0)) {
          bonus -= 1.0;
        }
        break;

      case FigureTypes.rook:
        // Open file bonus
        bonus += 1.0; // Base bonus for rook activity

        // 7th rank bonus
        if ((color == GameColors.white && y == 1) ||
            (color == GameColors.black && y == 6)) {
          bonus += 2.0;
        }
        break;

      case FigureTypes.queen:
        // Queen activity bonus but penalty for early development
        final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
        bonus += (4 - centerDistance) * 0.3;

        // Penalty for early queen moves
        if ((color == GameColors.white && y < 6) ||
            (color == GameColors.black && y > 1)) {
          bonus -= 0.5;
        }
        break;

      case FigureTypes.king:
        // King safety evaluation
        final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;
        bonus += centerDistance * 0.4; // Stay away from center

        // Castling position bonus
        if ((color == GameColors.white && y == 7 && (x == 1 || x == 6)) ||
            (color == GameColors.black && y == 0 && (x == 1 || x == 6))) {
          bonus += 1.5;
        }
        break;
    }

    return bonus;
  }

  double _evaluateAllPositions(Board board, GameColors color) {
    double score = 0.0;

    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied && cell.getFigure()!.color == color) {
          score += _getPositionalValue(cell.getFigure()!.type, x, y, color);
        }
      }
    }

    return score;
  }

  double _getPositionalValue(FigureTypes type, int x, int y, GameColors color) {
    final centerDistance = ((x - 3.5).abs() + (y - 3.5).abs()) / 2;

    switch (type) {
      case FigureTypes.pawn:
        // Pawns advance towards promotion
        return color == GameColors.white ? (7 - y) * 0.5 : y * 0.5;
      case FigureTypes.knight:
        // Knights prefer center
        return 4 - centerDistance;
      case FigureTypes.bishop:
        // Bishops prefer long diagonals
        return _getDiagonalControlScore(x, y);
      case FigureTypes.rook:
        // Rooks prefer open files
        return 2.0;
      case FigureTypes.queen:
        // Queen prefers center but not too early
        return 2 - centerDistance * 0.5;
      case FigureTypes.king:
        // King prefers safety (corners early game)
        return centerDistance * 0.5;
    }
  }

  double _evaluateKingSafetyForBoard(Board board, GameColors color) {
    // Find king position and evaluate safety
    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied &&
            cell.getFigure()!.type == FigureTypes.king &&
            cell.getFigure()!.color == color) {
          return _evaluateKingSafety(board, x, y, color);
        }
      }
    }
    return 0.0;
  }

  double _evaluatePawnStructureForBoard(Board board, GameColors color) {
    double score = 0.0;

    // Count doubled pawns, isolated pawns, etc.
    for (int x = 0; x < 8; x++) {
      int pawnCount = 0;
      for (int y = 0; y < 8; y++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied &&
            cell.getFigure()!.type == FigureTypes.pawn &&
            cell.getFigure()!.color == color) {
          pawnCount++;
        }
      }

      // Penalty for doubled pawns
      if (pawnCount > 1) {
        score -= (pawnCount - 1) * 0.5;
      }
    }

    return score;
  }
}
