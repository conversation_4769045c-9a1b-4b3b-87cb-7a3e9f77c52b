import 'dart:math';
import 'package:chess_app/models/board.dart';
import 'package:chess_app/models/cell.dart';
import 'package:chess_app/models/figure_types.dart';
import 'package:chess_app/models/game_colors.dart';

/// Simple AI that makes random valid moves
class SimpleAI {
  final Random _random = Random();

  /// Get a random valid move for the given color
  Future<String?> getBestMove(Board board, GameColors color) async {
    // Simulate thinking time
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1500)));

    final validMoves = _getAllValidMoves(board, color);

    if (validMoves.isEmpty) {
      return null;
    }

    // Return a random valid move
    return validMoves[_random.nextInt(validMoves.length)];
  }

  List<String> _getAllValidMoves(Board board, GameColors color) {
    final List<String> moves = [];

    // Find all pieces of the given color
    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final cell = board.getCellAt(x, y);
        if (cell.occupied && cell.getFigure()!.color == color) {
          // Find all valid moves for this piece
          final pieceMoves = _getValidMovesForPiece(board, cell);
          moves.addAll(pieceMoves);
        }
      }
    }

    return moves;
  }

  List<String> _getValidMovesForPiece(Board board, Cell fromCell) {
    final List<String> moves = [];
    final figure = fromCell.getFigure()!;

    // Check all possible destination squares
    for (int y = 0; y < 8; y++) {
      for (int x = 0; x < 8; x++) {
        final toCell = board.getCellAt(x, y);

        // Check if this move is valid
        if (figure.availableForMove(toCell)) {
          final fromUci =
              _positionToUci(fromCell.position.x, fromCell.position.y);
          final toUci = _positionToUci(x, y);

          // Check for pawn promotion
          if (figure.type == FigureTypes.pawn) {
            final isWhitePawnPromotion =
                figure.color == GameColors.white && y == 0;
            final isBlackPawnPromotion =
                figure.color == GameColors.black && y == 7;

            if (isWhitePawnPromotion || isBlackPawnPromotion) {
              // Add promotion to queen
              moves.add('$fromUci${toUci}q');
            } else {
              moves.add('$fromUci$toUci');
            }
          } else {
            moves.add('$fromUci$toUci');
          }
        }
      }
    }

    return moves;
  }

  String _positionToUci(int x, int y) {
    final file = String.fromCharCode('a'.codeUnitAt(0) + x);
    final rank = (8 - y).toString();
    return '$file$rank';
  }
}
