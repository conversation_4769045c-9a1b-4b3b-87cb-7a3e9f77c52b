import 'package:chess_app/bloc/cubits/settings_cubit.dart';
import 'package:chess_app/bloc/states/settings_state.dart';
import 'package:chess_app/screens/game_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Game Settings'),
        backgroundColor: Colors.brown[700],
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<SettingsCubit, SettingsState>(
        bloc: GetIt.I<SettingsCubit>(),
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Game Settings',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text('White: ${state.whitePlayer.isHuman ? "Human" : "AI"}'),
                Text('Black: ${state.blackPlayer.isHuman ? "Human" : "AI"}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => const GameScreen(),
                      ),
                    );
                  },
                  child: const Text('Start Game'),
                ),
                const SizedBox(height: 16),
                Text('AI Difficulty: ${state.difficulty}/5'),
                const SizedBox(height: 8),
                Text('Tip: Bây giờ bạn có thể bắt vua để test chức năng game over!',
                     style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildGameModeSelector(BuildContext context, SettingsState state) {
    final settingsCubit = GetIt.I<SettingsCubit>();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildGameModeOption(
          context,
          'Human vs Human',
          'Two players take turns',
          GameMode.humanVsHuman,
          _getCurrentGameMode(state),
          settingsCubit,
        ),
        _buildGameModeOption(
          context,
          'Human vs AI',
          'Play against computer (you are white)',
          GameMode.humanVsAI,
          _getCurrentGameMode(state),
          settingsCubit,
        ),
        _buildGameModeOption(
          context,
          'AI vs Human',
          'Play against computer (you are black)',
          GameMode.aiVsHuman,
          _getCurrentGameMode(state),
          settingsCubit,
        ),
        _buildGameModeOption(
          context,
          'AI vs AI',
          'Watch two AIs play against each other',
          GameMode.aiVsAI,
          _getCurrentGameMode(state),
          settingsCubit,
        ),
      ],
    );
  }

  Widget _buildGameModeOption(
    BuildContext context,
    String title,
    String subtitle,
    GameMode mode,
    GameMode currentMode,
    SettingsCubit settingsCubit,
  ) {
    return Card(
      child: InkWell(
        onTap: () {
          settingsCubit.setGameMode(mode);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Radio<GameMode>(
                    value: mode,
                    groupValue: currentMode,
                    onChanged: (GameMode? value) {
                      if (value != null) {
                        settingsCubit.setGameMode(value);
                      }
                    },
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(left: 48.0),
                child: Text(
                  subtitle,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultySlider(BuildContext context, SettingsState state) {
    final settingsCubit = GetIt.I<SettingsCubit>();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Difficulty: ${_getDifficultyName(state.difficulty)}',
              style: const TextStyle(fontSize: 16),
            ),
            Container(
              height: 40,
              child: Slider(
                value: state.difficulty.toDouble(),
                min: 1,
                max: 5,
                divisions: 4,
                onChanged: state.hasAI
                    ? (double value) {
                        settingsCubit.setDifficulty(value.round());
                      }
                    : null,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: const [
                Text('Easy'),
                Text('Hard'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerInfo(SettingsState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Players',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.circle, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text('White: ${state.whitePlayer.name}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.circle, color: Colors.black, size: 16),
                const SizedBox(width: 8),
                Text('Black: ${state.blackPlayer.name}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  GameMode _getCurrentGameMode(SettingsState state) {
    if (state.whitePlayer.isHuman && state.blackPlayer.isHuman) {
      return GameMode.humanVsHuman;
    } else if (state.whitePlayer.isHuman && !state.blackPlayer.isHuman) {
      return GameMode.humanVsAI;
    } else if (!state.whitePlayer.isHuman && state.blackPlayer.isHuman) {
      return GameMode.aiVsHuman;
    } else {
      return GameMode.aiVsAI;
    }
  }

  String _getDifficultyName(int difficulty) {
    switch (difficulty) {
      case 1:
        return 'Very Easy';
      case 2:
        return 'Easy';
      case 3:
        return 'Medium';
      case 4:
        return 'Hard';
      case 5:
        return 'Very Hard';
      default:
        return 'Medium';
    }
  }
}
